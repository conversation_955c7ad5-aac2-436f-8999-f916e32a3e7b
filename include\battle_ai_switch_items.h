#ifndef GUARD_BATTLE_AI_SWITCH_ITEMS_H
#define GUARD_BATTLE_AI_SWITCH_ITEMS_H

enum ShouldSwitchScenario
{
    SHOULD_SWITCH_WONDER_GUARD,
    SHOULD_SWITCH_ABSORBS_MOVE,
    SHOULD_SWITCH_TRAPPER,
    SHOULD_SWITCH_FREE_TURN,
    SHOULD_SWITCH_TRUANT,
    SHOULD_SWITCH_ALL_MOVES_BAD,
    SHOULD_SWITCH_PERISH_SONG,
    SHOULD_SWITCH_YAWN,
    SHOULD_SWITCH_BADLY_POISONED,
    SHOULD_SWITCH_BADLY_POISONED_STATS_RAISED,
    SHOULD_SWITCH_CURSED,
    SHOULD_SWITCH_CURSED_STATS_RAISED,
    SHOULD_SWITCH_NIGHTMARE,
    SHOULD_SWITCH_NIGHTMARE_STATS_RAISED,
    SHOULD_SWITCH_SEEDED,
    SHOULD_SWITCH_SEEDED_STATS_RAISED,
    SHOULD_SWITCH_INFATUATION,
    SHOULD_SWITCH_HASBADODDS,
    SHOULD_SWITCH_NATURAL_CURE_STRONG,
    SHOULD_SWITCH_NATURAL_CURE_STRONG_STATS_RAISED,
    SHOULD_SWITCH_NATURAL_CURE_WEAK,
    SHOULD_SWITCH_NATURAL_CURE_WEAK_STATS_RAISED,
    SHOULD_SWITCH_REGENERATOR,
    SHOULD_SWITCH_REGENERATOR_STATS_RAISED,
    SHOULD_SWITCH_ENCORE_STATUS,
    SHOULD_SWITCH_ENCORE_DAMAGE,
    SHOULD_SWITCH_CHOICE_LOCKED,
    SHOULD_SWITCH_ATTACKING_STAT_MINUS_TWO,
    SHOULD_SWITCH_ATTACKING_STAT_MINUS_THREE_PLUS,
};

enum SwitchType
{
    SWITCH_AFTER_KO,
    SWITCH_MID_BATTLE,
};

// Debug categories for SetBattleAISwitchItemsDebug
#define DEBUG_SWITCH_DECISIONS      0
#define DEBUG_ITEM_DECISIONS        1
#define DEBUG_SWITCH_REASONS        2
#define DEBUG_MON_EVALUATION        3
#define DEBUG_TYPE_MATCHUPS         4
#define DEBUG_DAMAGE_CALCULATIONS   5
#define DEBUG_HAZARD_CALCULATIONS   6
#define DEBUG_STATUS_CONDITIONS     7
#define DEBUG_ABILITY_CHECKS        8
#define DEBUG_PLAYER_PREDICTION     9
#define DEBUG_ALL_CATEGORIES        99

void GetAIPartyIndexes(u32 battlerId, s32 *firstId, s32 *lastId);
void AI_TrySwitchOrUseItem(u32 battler);
u32 GetMostSuitableMonToSwitchInto(u32 battler, enum SwitchType switchType);
bool32 ShouldSwitch(u32 battler);
bool32 IsMonGrounded(u16 heldItemEffect, u32 ability, u8 type1, u8 type2);

bool32 HasHealItemWithEffect(u8 battleUsageID);

// Debug function to toggle debug output categories
void SetBattleAISwitchItemsDebug(u32 category, bool32 enabled);

#endif // GUARD_BATTLE_AI_SWITCH_ITEMS_H
