#ifndef GUARD_CONSTANTS_ABILITIES_H
#define GUAR<PERSON>_CONSTANTS_ABILITIES_H

#define ABILITY_NONE 0
#define ABILITY_STENCH 1
#define ABILITY_DRIZZLE 2
#define ABILITY_SPEED_BOOST 3
#define ABILITY_BATTLE_ARMOR 4
#define ABILITY_STURDY 5
#define ABILITY_DAMP 6
#define ABILITY_LIMBER 7
#define ABILITY_SAND_VEIL 8
#define ABILITY_STATIC 9
#define ABILITY_VOLT_ABSORB 10
#define ABILITY_WATER_ABSORB 11
#define ABILITY_OBLIVIOUS 12
#define ABILITY_CLOUD_NINE 13
#define ABILITY_COMPOUND_EYES 14
#define ABILITY_INSOMNIA 15
#define ABILITY_COLOR_CHANGE 16
#define ABILITY_IMMUNITY 17
#define ABILITY_FLASH_FIRE 18
#define ABILITY_SHIELD_DUST 19
#define AB<PERSON><PERSON>Y_OWN_TEMPO 20
#define ABILITY_SUCTION_CUPS 21
#define AB<PERSON><PERSON>Y_INTIMIDATE 22
#define ABILITY_SHADOW_TAG 23
#define ABILITY_ROUGH_SKIN 24
#define ABILITY_WONDER_GUARD 25
#define ABILITY_LEVITATE 26
#define ABILITY_EFFECT_SPORE 27
#define ABILITY_SYNCHRONIZE 28
#define ABILITY_CLEAR_BODY 29
#define ABILITY_NATURAL_CURE 30
#define ABILITY_LIGHTNING_ROD 31
#define ABILITY_SERENE_GRACE 32
#define ABILITY_SWIFT_SWIM 33
#define ABILITY_CHLOROPHYLL 34
#define ABILITY_ILLUMINATE 35
#define ABILITY_TRACE 36
#define ABILITY_HUGE_POWER 37
#define ABILITY_POISON_POINT 38
#define ABILITY_INNER_FOCUS 39
#define ABILITY_MAGMA_ARMOR 40
#define ABILITY_WATER_VEIL 41
#define ABILITY_MAGNET_PULL 42
#define ABILITY_SOUNDPROOF 43
#define ABILITY_RAIN_DISH 44
#define ABILITY_SAND_STREAM 45
#define ABILITY_PRESSURE 46
#define ABILITY_THICK_FAT 47
#define ABILITY_EARLY_BIRD 48
#define ABILITY_FLAME_BODY 49
#define ABILITY_RUN_AWAY 50
#define ABILITY_KEEN_EYE 51
#define ABILITY_HYPER_CUTTER 52
#define ABILITY_PICKUP 53
#define ABILITY_TRUANT 54
#define ABILITY_HUSTLE 55
#define ABILITY_CUTE_CHARM 56
#define ABILITY_PLUS 57
#define ABILITY_MINUS 58
#define ABILITY_FORECAST 59
#define ABILITY_STICKY_HOLD 60
#define ABILITY_SHED_SKIN 61
#define ABILITY_GUTS 62
#define ABILITY_MARVEL_SCALE 63
#define ABILITY_LIQUID_OOZE 64
#define ABILITY_OVERGROW 65
#define ABILITY_BLAZE 66
#define ABILITY_TORRENT 67
#define ABILITY_SWARM 68
#define ABILITY_ROCK_HEAD 69
#define ABILITY_DROUGHT 70
#define ABILITY_ARENA_TRAP 71
#define ABILITY_VITAL_SPIRIT 72
#define ABILITY_WHITE_SMOKE 73
#define ABILITY_PURE_POWER 74
#define ABILITY_SHELL_ARMOR 75
#define ABILITY_AIR_LOCK 76

#define ABILITIES_COUNT_GEN3 77

// Gen 4
#define ABILITY_TANGLED_FEET 77
#define ABILITY_MOTOR_DRIVE 78
#define ABILITY_RIVALRY 79
#define ABILITY_STEADFAST 80
#define ABILITY_SNOW_CLOAK 81
#define ABILITY_GLUTTONY 82
#define ABILITY_ANGER_POINT 83
#define ABILITY_UNBURDEN 84
#define ABILITY_HEATPROOF 85
#define ABILITY_SIMPLE 86
#define ABILITY_DRY_SKIN 87
#define ABILITY_DOWNLOAD 88
#define ABILITY_IRON_FIST 89
#define ABILITY_POISON_HEAL 90
#define ABILITY_ADAPTABILITY 91
#define ABILITY_SKILL_LINK 92
#define ABILITY_HYDRATION 93
#define ABILITY_SOLAR_POWER 94
#define ABILITY_QUICK_FEET 95
#define ABILITY_NORMALIZE 96
#define ABILITY_SNIPER 97
#define ABILITY_MAGIC_GUARD 98
#define ABILITY_NO_GUARD 99
#define ABILITY_STALL 100
#define ABILITY_TECHNICIAN 101
#define ABILITY_LEAF_GUARD 102
#define ABILITY_KLUTZ 103
#define ABILITY_MOLD_BREAKER 104
#define ABILITY_SUPER_LUCK 105
#define ABILITY_AFTERMATH 106
#define ABILITY_ANTICIPATION 107
#define ABILITY_FOREWARN 108
#define ABILITY_UNAWARE 109
#define ABILITY_TINTED_LENS 110
#define ABILITY_FILTER 111
#define ABILITY_SLOW_START 112
#define ABILITY_SCRAPPY 113
#define ABILITY_STORM_DRAIN 114
#define ABILITY_ICE_BODY 115
#define ABILITY_SOLID_ROCK 116
#define ABILITY_SNOW_WARNING 117
#define ABILITY_HONEY_GATHER 118
#define ABILITY_FRISK 119
#define ABILITY_RECKLESS 120
#define ABILITY_MULTITYPE 121
#define ABILITY_FLOWER_GIFT 122
#define ABILITY_BAD_DREAMS 123

#define ABILITIES_COUNT_GEN4 124

// Gen 5
#define ABILITY_PICKPOCKET 124
#define ABILITY_SHEER_FORCE 125
#define ABILITY_CONTRARY 126
#define ABILITY_UNNERVE 127
#define ABILITY_DEFIANT 128
#define ABILITY_DEFEATIST 129
#define ABILITY_CURSED_BODY 130
#define ABILITY_HEALER 131
#define ABILITY_FRIEND_GUARD 132
#define ABILITY_WEAK_ARMOR 133
#define ABILITY_HEAVY_METAL 134
#define ABILITY_LIGHT_METAL 135
#define ABILITY_MULTISCALE 136
#define ABILITY_TOXIC_BOOST 137
#define ABILITY_FLARE_BOOST 138
#define ABILITY_HARVEST 139
#define ABILITY_TELEPATHY 140
#define ABILITY_MOODY 141
#define ABILITY_OVERCOAT 142
#define ABILITY_POISON_TOUCH 143
#define ABILITY_REGENERATOR 144
#define ABILITY_BIG_PECKS 145
#define ABILITY_SAND_RUSH 146
#define ABILITY_WONDER_SKIN 147
#define ABILITY_ANALYTIC 148
#define ABILITY_ILLUSION 149
#define ABILITY_IMPOSTER 150
#define ABILITY_INFILTRATOR 151
#define ABILITY_MUMMY 152
#define ABILITY_MOXIE 153
#define ABILITY_JUSTIFIED 154
#define ABILITY_RATTLED 155
#define ABILITY_MAGIC_BOUNCE 156
#define ABILITY_SAP_SIPPER 157
#define ABILITY_PRANKSTER 158
#define ABILITY_SAND_FORCE 159
#define ABILITY_IRON_BARBS 160
#define ABILITY_ZEN_MODE 161
#define ABILITY_VICTORY_STAR 162
#define ABILITY_TURBOBLAZE 163
#define ABILITY_TERAVOLT 164

#define ABILITIES_COUNT_GEN5 165

// Gen 6
#define ABILITY_AROMA_VEIL 165
#define ABILITY_FLOWER_VEIL 166
#define ABILITY_CHEEK_POUCH 167
#define ABILITY_PROTEAN 168
#define ABILITY_FUR_COAT 169
#define ABILITY_MAGICIAN 170
#define ABILITY_BULLETPROOF 171
#define ABILITY_COMPETITIVE 172
#define ABILITY_STRONG_JAW 173
#define ABILITY_REFRIGERATE 174
#define ABILITY_SWEET_VEIL 175
#define ABILITY_STANCE_CHANGE 176
#define ABILITY_GALE_WINGS 177
#define ABILITY_MEGA_LAUNCHER 178
#define ABILITY_GRASS_PELT 179
#define ABILITY_SYMBIOSIS 180
#define ABILITY_TOUGH_CLAWS 181
#define ABILITY_PIXILATE 182
#define ABILITY_GOOEY 183
#define ABILITY_AERILATE 184
#define ABILITY_PARENTAL_BOND 185
#define ABILITY_DARK_AURA 186
#define ABILITY_FAIRY_AURA 187
#define ABILITY_AURA_BREAK 188
#define ABILITY_PRIMORDIAL_SEA 189
#define ABILITY_DESOLATE_LAND 190
#define ABILITY_DELTA_STREAM 191

#define ABILITIES_COUNT_GEN6 192

// Gen 7
#define ABILITY_STAMINA 192
#define ABILITY_WIMP_OUT 193
#define ABILITY_EMERGENCY_EXIT 194
#define ABILITY_WATER_COMPACTION 195
#define ABILITY_MERCILESS 196
#define ABILITY_SHIELDS_DOWN 197
#define ABILITY_STAKEOUT 198
#define ABILITY_WATER_BUBBLE 199
#define ABILITY_STEELWORKER 200
#define ABILITY_BERSERK 201
#define ABILITY_SLUSH_RUSH 202
#define ABILITY_LONG_REACH 203
#define ABILITY_LIQUID_VOICE 204
#define ABILITY_TRIAGE 205
#define ABILITY_GALVANIZE 206
#define ABILITY_SURGE_SURFER 207
#define ABILITY_SCHOOLING 208
#define ABILITY_DISGUISE 209
#define ABILITY_BATTLE_BOND 210
#define ABILITY_POWER_CONSTRUCT 211
#define ABILITY_CORROSION 212
#define ABILITY_COMATOSE 213
#define ABILITY_QUEENLY_MAJESTY 214
#define ABILITY_INNARDS_OUT 215
#define ABILITY_DANCER 216
#define ABILITY_BATTERY 217
#define ABILITY_FLUFFY 218
#define ABILITY_DAZZLING 219
#define ABILITY_SOUL_HEART 220
#define ABILITY_TANGLING_HAIR 221
#define ABILITY_RECEIVER 222
#define ABILITY_POWER_OF_ALCHEMY 223
#define ABILITY_BEAST_BOOST 224
#define ABILITY_RKS_SYSTEM 225
#define ABILITY_ELECTRIC_SURGE 226
#define ABILITY_PSYCHIC_SURGE 227
#define ABILITY_MISTY_SURGE 228
#define ABILITY_GRASSY_SURGE 229
#define ABILITY_FULL_METAL_BODY 230
#define ABILITY_SHADOW_SHIELD 231
#define ABILITY_PRISM_ARMOR 232
#define ABILITY_NEUROFORCE 233

#define ABILITIES_COUNT_GEN7 234

// Gen 8
#define ABILITY_INTREPID_SWORD 234
#define ABILITY_DAUNTLESS_SHIELD 235
#define ABILITY_LIBERO 236
#define ABILITY_BALL_FETCH 237
#define ABILITY_COTTON_DOWN 238
#define ABILITY_PROPELLER_TAIL 239
#define ABILITY_MIRROR_ARMOR 240
#define ABILITY_GULP_MISSILE 241
#define ABILITY_STALWART 242
#define ABILITY_STEAM_ENGINE 243
#define ABILITY_PUNK_ROCK 244
#define ABILITY_SAND_SPIT 245
#define ABILITY_ICE_SCALES 246
#define ABILITY_RIPEN 247
#define ABILITY_ICE_FACE 248
#define ABILITY_POWER_SPOT 249
#define ABILITY_MIMICRY 250
#define ABILITY_SCREEN_CLEANER 251
#define ABILITY_STEELY_SPIRIT 252
#define ABILITY_PERISH_BODY 253
#define ABILITY_WANDERING_SPIRIT 254
#define ABILITY_GORILLA_TACTICS 255
#define ABILITY_NEUTRALIZING_GAS 256
#define ABILITY_PASTEL_VEIL 257
#define ABILITY_HUNGER_SWITCH 258
#define ABILITY_QUICK_DRAW 259
#define ABILITY_UNSEEN_FIST 260
#define ABILITY_CURIOUS_MEDICINE 261
#define ABILITY_TRANSISTOR 262
#define ABILITY_DRAGONS_MAW 263
#define ABILITY_CHILLING_NEIGH 264
#define ABILITY_GRIM_NEIGH 265
#define ABILITY_AS_ONE_ICE_RIDER 266
#define ABILITY_AS_ONE_SHADOW_RIDER 267

#define ABILITIES_COUNT_GEN8 268

// Gen 9
#define ABILITY_LINGERING_AROMA 268
#define ABILITY_SEED_SOWER 269
#define ABILITY_THERMAL_EXCHANGE 270
#define ABILITY_ANGER_SHELL 271
#define ABILITY_PURIFYING_SALT 272
#define ABILITY_WELL_BAKED_BODY 273
#define ABILITY_WIND_RIDER 274
#define ABILITY_GUARD_DOG 275
#define ABILITY_ROCKY_PAYLOAD 276
#define ABILITY_WIND_POWER 277
#define ABILITY_ZERO_TO_HERO 278
#define ABILITY_COMMANDER 279
#define ABILITY_ELECTROMORPHOSIS 280
#define ABILITY_PROTOSYNTHESIS 281
#define ABILITY_QUARK_DRIVE 282
#define ABILITY_GOOD_AS_GOLD 283
#define ABILITY_VESSEL_OF_RUIN 284
#define ABILITY_SWORD_OF_RUIN 285
#define ABILITY_TABLETS_OF_RUIN 286
#define ABILITY_BEADS_OF_RUIN 287
#define ABILITY_ORICHALCUM_PULSE 288
#define ABILITY_HADRON_ENGINE 289
#define ABILITY_OPPORTUNIST 290
#define ABILITY_CUD_CHEW 291
#define ABILITY_SHARPNESS 292
#define ABILITY_SUPREME_OVERLORD 293
#define ABILITY_COSTAR 294
#define ABILITY_TOXIC_DEBRIS 295
#define ABILITY_ARMOR_TAIL 296
#define ABILITY_EARTH_EATER 297
#define ABILITY_MYCELIUM_MIGHT 298
#define ABILITY_HOSPITALITY 299
#define ABILITY_MINDS_EYE 300
#define ABILITY_EMBODY_ASPECT_TEAL_MASK 301
#define ABILITY_EMBODY_ASPECT_HEARTHFLAME_MASK 302
#define ABILITY_EMBODY_ASPECT_WELLSPRING_MASK 303
#define ABILITY_EMBODY_ASPECT_CORNERSTONE_MASK 304
#define ABILITY_TOXIC_CHAIN 305
#define ABILITY_SUPERSWEET_SYRUP 306
#define ABILITY_TERA_SHIFT 307
#define ABILITY_TERA_SHELL 308
#define ABILITY_TERAFORM_ZERO 309
#define ABILITY_POISON_PUPPETEER 310

#define ABILITIES_COUNT_GEN9 311

#define ABILITY_FUR_LAYER ABILITIES_COUNT_GEN9 + 0
#define ABILITY_SOLAR_CORE ABILITIES_COUNT_GEN9 + 1
#define ABILITY_MERRY ABILITIES_COUNT_GEN9 + 2
#define ABILITY_DEDICATED ABILITIES_COUNT_GEN9 + 3
#define ABILITY_ASTRAL_CHARGE ABILITIES_COUNT_GEN9 + 4
#define ABILITY_MYSTIC ABILITIES_COUNT_GEN9 + 5
#define ABILITY_DOMINATE ABILITIES_COUNT_GEN9 + 6

#define ABILITIES_COUNT_CUSTOM ABILITIES_COUNT_GEN9 + 7

#define ABILITIES_COUNT ABILITIES_COUNT_CUSTOM

#endif  // GUARD_CONSTANTS_ABILITIES_H
