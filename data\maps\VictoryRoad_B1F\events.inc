@
@ DO NOT MODIFY THIS FILE! It is auto-generated from data/maps/VictoryRoad_B1F/map.json
@

	.align 2

VictoryRoad_B1F_ObjectEvents:
	object_event 1, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 20, 5, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_13
	object_event 2, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 21, 4, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_14
	object_event 3, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 4, 7, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, <PERSON>AG_TEMP_11
	object_event 4, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 9, 10, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_12
	object_event 5, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 20, 26, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_17
	object_event 6, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 21, 25, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_16
	object_event 7, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 35, 6, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_1E
	object_event 8, OBJ_EVENT_GFX_BREAKABLE_ROCK, 19, 5, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_RockSmash, FLAG_TEMP_19
	object_event 9, OBJ_EVENT_GFX_BREAKABLE_ROCK, 20, 4, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_RockSmash, FLAG_TEMP_1A
	object_event 10, OBJ_EVENT_GFX_BREAKABLE_ROCK, 18, 12, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_RockSmash, FLAG_TEMP_1B
	object_event 11, OBJ_EVENT_GFX_BREAKABLE_ROCK, 20, 25, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_RockSmash, FLAG_TEMP_1C
	object_event 12, OBJ_EVENT_GFX_BREAKABLE_ROCK, 21, 26, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_RockSmash, FLAG_TEMP_1D
	object_event 13, OBJ_EVENT_GFX_PUSHABLE_BOULDER, 34, 4, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, EventScript_StrengthBoulder, FLAG_TEMP_1F
	object_event 14, OBJ_EVENT_GFX_MAN_3, 37, 12, 3, MOVEMENT_TYPE_FACE_LEFT, 0, 0, TRAINER_TYPE_NORMAL, 3, VictoryRoad_B1F_EventScript_Samuel, 0
	object_event 15, OBJ_EVENT_GFX_WOMAN_5, 26, 16, 3, MOVEMENT_TYPE_FACE_UP, 0, 0, TRAINER_TYPE_NORMAL, 4, VictoryRoad_B1F_EventScript_Shannon, 0
	object_event 16, OBJ_EVENT_GFX_WOMAN_5, 5, 21, 3, MOVEMENT_TYPE_FACE_LEFT, 0, 0, TRAINER_TYPE_NORMAL, 2, VictoryRoad_B1F_EventScript_Michelle, 0
	object_event 17, OBJ_EVENT_GFX_BREAKABLE_ROCK, 34, 3, 3, MOVEMENT_TYPE_LOOK_AROUND, 1, 1, TRAINER_TYPE_NONE, 0, EventScript_RockSmash, FLAG_TEMP_15
	object_event 18, OBJ_EVENT_GFX_ITEM_BALL, 42, 8, 4, MOVEMENT_TYPE_LOOK_AROUND, 1, 1, TRAINER_TYPE_NONE, ITEM_TM_PSYCHIC, Common_EventScript_FindItem, FLAG_ITEM_VICTORY_ROAD_B1F_TM_PSYCHIC
	object_event 19, OBJ_EVENT_GFX_ITEM_BALL, 32, 3, 3, MOVEMENT_TYPE_LOOK_AROUND, 1, 1, TRAINER_TYPE_NONE, ITEM_FULL_RESTORE, Common_EventScript_FindItem, FLAG_ITEM_VICTORY_ROAD_B1F_FULL_RESTORE
	object_event 20, OBJ_EVENT_GFX_MAN_3, 14, 16, 3, MOVEMENT_TYPE_FACE_DOWN, 1, 1, TRAINER_TYPE_NORMAL, 4, VictoryRoad_B1F_EventScript_Mitchell, 0
	object_event 21, OBJ_EVENT_GFX_WOMAN_5, 14, 20, 3, MOVEMENT_TYPE_FACE_UP_AND_RIGHT, 1, 1, TRAINER_TYPE_NORMAL, 3, VictoryRoad_B1F_EventScript_Halle, 0

VictoryRoad_B1F_MapWarps:
	warp_def 30, 25, 3, 0, MAP_VICTORY_ROAD_B2F
	warp_def 17, 16, 3, 2, MAP_VICTORY_ROAD_B2F
	warp_def 42, 25, 3, 3, MAP_VICTORY_ROAD_1F
	warp_def 42, 2, 4, 1, MAP_VICTORY_ROAD_B2F
	warp_def 8, 3, 3, 4, MAP_VICTORY_ROAD_1F
	warp_def 20, 21, 3, 2, MAP_VICTORY_ROAD_1F
	warp_def 5, 26, 3, 3, MAP_VICTORY_ROAD_B2F

VictoryRoad_B1F_MapEvents::
	map_events VictoryRoad_B1F_ObjectEvents, VictoryRoad_B1F_MapWarps, NULL, NULL

