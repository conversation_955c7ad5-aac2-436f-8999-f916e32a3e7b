@
@ DO NOT MODIFY THIS FILE! It is auto-generated from data/maps/SootopolisCity/map.json
@

	.align 2

SootopolisCity_ObjectEvents:
	object_event 1, OBJ_EVENT_GFX_EXPERT_M, 31, 18, 3, MOVEMENT_TYPE_FACE_DOWN, 0, 0, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_CaveOfOriginExpert, 0
	object_event 2, OBJ_EVENT_GFX_WOMAN_4, 47, 33, 0, MOVEMENT_TYPE_FACE_LEFT, 0, 0, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Woman2, FLAG_HIDE_SOOTOPOLIS_CITY_RESIDENTS
	object_event 3, OBJ_EVENT_GFX_GIRL_2, 9, 43, 3, MOVEMENT_TYPE_WANDER_LEFT_AND_RIGHT, 1, 0, TRAINER_TYPE_NONE, 0, <PERSON><PERSON><PERSON>City_EventScript_Ki<PERSON>, 0
	object_event 4, OBJ_EVENT_GFX_NINJA_BOY, 51, 14, 3, MOVEMENT_TYPE_FACE_DOWN, 0, 0, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_NinjaBoy, 0
	object_event 5, OBJ_EVENT_GFX_BOY_1, 43, 26, 3, MOVEMENT_TYPE_LOOK_AROUND, 0, 0, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Boy1, 0
	object_event 6, OBJ_EVENT_GFX_MAN_1, 26, 4, 3, MOVEMENT_TYPE_WANDER_LEFT_AND_RIGHT, 1, 0, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Man, FLAG_HIDE_SOOTOPOLIS_CITY_MAN_1
	object_event 7, OBJ_EVENT_GFX_STEVEN, 20, 36, 3, MOVEMENT_TYPE_FACE_DOWN, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Steven, FLAG_HIDE_SOOTOPOLIS_CITY_STEVEN
	object_event 8, OBJ_EVENT_GFX_WOMAN_5, 49, 34, 3, MOVEMENT_TYPE_WANDER_UP_AND_DOWN, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Woman1, 0
	object_event 9, OBJ_EVENT_GFX_GROUDON_SIDE, 28, 44, 0, MOVEMENT_TYPE_WALK_SLOWLY_IN_PLACE_RIGHT, 1, 1, TRAINER_TYPE_NONE, 0, 0x0, FLAG_HIDE_SOOTOPOLIS_CITY_GROUDON
	object_event 10, OBJ_EVENT_GFX_KYOGRE_SIDE, 34, 44, 1, MOVEMENT_TYPE_WALK_SLOWLY_IN_PLACE_LEFT, 1, 1, TRAINER_TYPE_NONE, 0, 0x0, FLAG_HIDE_SOOTOPOLIS_CITY_KYOGRE
	object_event 11, OBJ_EVENT_GFX_RAYQUAZA, 31, 41, 1, MOVEMENT_TYPE_FACE_RIGHT, 1, 1, TRAINER_TYPE_NONE, 0, 0x0, FLAG_HIDE_SOOTOPOLIS_CITY_RAYQUAZA
	object_event 12, OBJ_EVENT_GFX_MANIAC, 17, 44, 3, MOVEMENT_TYPE_FACE_RIGHT, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Maniac, FLAG_HIDE_SOOTOPOLIS_CITY_RESIDENTS
	object_event 13, OBJ_EVENT_GFX_GIRL_3, 14, 42, 3, MOVEMENT_TYPE_FACE_RIGHT, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Girl, FLAG_HIDE_SOOTOPOLIS_CITY_RESIDENTS
	object_event 14, OBJ_EVENT_GFX_BLACK_BELT, 17, 40, 3, MOVEMENT_TYPE_FACE_RIGHT, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_BlackBelt, FLAG_HIDE_SOOTOPOLIS_CITY_RESIDENTS
	object_event 15, OBJ_EVENT_GFX_BOY_2, 19, 37, 3, MOVEMENT_TYPE_FACE_DOWN, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Boy2, FLAG_HIDE_SOOTOPOLIS_CITY_RESIDENTS
	object_event 16, OBJ_EVENT_GFX_MAXIE, 29, 33, 3, MOVEMENT_TYPE_FACE_DOWN, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Maxie, FLAG_HIDE_SOOTOPOLIS_CITY_MAXIE
	object_event 17, OBJ_EVENT_GFX_ARCHIE, 31, 33, 3, MOVEMENT_TYPE_FACE_DOWN, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Archie, FLAG_HIDE_SOOTOPOLIS_CITY_ARCHIE
	object_event 18, OBJ_EVENT_GFX_WALLACE, 31, 18, 3, MOVEMENT_TYPE_FACE_DOWN, 1, 1, TRAINER_TYPE_NONE, 0, SootopolisCity_EventScript_Wallace, FLAG_HIDE_SOOTOPOLIS_CITY_WALLACE

SootopolisCity_MapWarps:
	warp_def 43, 31, 0, 0, MAP_SOOTOPOLIS_CITY_POKEMON_CENTER_1F
	warp_def 17, 29, 0, 0, MAP_SOOTOPOLIS_CITY_MART
	warp_def 31, 32, 0, 0, MAP_SOOTOPOLIS_CITY_GYM_1F
	warp_def 31, 16, 3, 0, MAP_CAVE_OF_ORIGIN_ENTRANCE
	warp_def 9, 6, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE1
	warp_def 45, 6, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE2
	warp_def 9, 17, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE3
	warp_def 44, 17, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE4
	warp_def 9, 26, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE5
	warp_def 53, 28, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE6
	warp_def 8, 35, 0, 0, MAP_SOOTOPOLIS_CITY_HOUSE7
	warp_def 48, 25, 0, 0, MAP_SOOTOPOLIS_CITY_LOTAD_AND_SEEDOT_HOUSE
	warp_def 51, 36, 0, 0, MAP_SOOTOPOLIS_CITY_MYSTERY_EVENTS_HOUSE_1F

SootopolisCity_MapBGEvents:
	bg_sign_event 33, 34, 3, BG_EVENT_PLAYER_FACING_ANY, SootopolisCity_EventScript_GymSign
	bg_sign_event 19, 29, 0, BG_EVENT_PLAYER_FACING_NORTH, Common_EventScript_ShowPokemartSign
	bg_sign_event 44, 31, 0, BG_EVENT_PLAYER_FACING_NORTH, Common_EventScript_ShowPokemonCenterSign
	bg_sign_event 45, 31, 0, BG_EVENT_PLAYER_FACING_NORTH, Common_EventScript_ShowPokemonCenterSign
	bg_sign_event 18, 29, 0, BG_EVENT_PLAYER_FACING_NORTH, Common_EventScript_ShowPokemartSign
	bg_sign_event 41, 37, 0, BG_EVENT_PLAYER_FACING_ANY, SootopolisCity_EventScript_CitySign

SootopolisCity_MapEvents::
	map_events SootopolisCity_ObjectEvents, SootopolisCity_MapWarps, NULL, SootopolisCity_MapBGEvents

